import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import store from './redux/store';
import MinimalRoutes from './MinimalRoutes';
import SimpleErrorBoundary from './components/SimpleErrorBoundary';

/**
 * Ultra-Minimal App Component - Optimized for Bundle Size
 * Includes Redux Provider for component state management
 */
const MinimalApp = () => {
  return (
    <Provider store={store}>
      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <SimpleErrorBoundary>
          <MinimalRoutes />
        </SimpleErrorBoundary>
      </Router>
    </Provider>
  );
};

export default MinimalApp;
