import { combineReducers } from 'redux';
import appReducer from './appReducer';
import websocketReducer from './websocketReducer';
import appDataReducer from './appDataReducer';
import webSocketClientReducer from './webSocketClientReducer';
import uiReducer from './uiReducer';
import apiKeysReducer from './apiKeysReducer';
import networkReducer from './networkReducer';
import themeReducer from './themeReducer';
import userReducer from './userReducer';
import projectReducer from './projectReducer';
import appBuilderReducer from './appBuilderReducer';

// Combine all reducers
const rootReducer = combineReducers({
  app: appReducer,
  websocket: websocketReducer,
  appData: appDataReducer,
  webSocketClient: webSocketClientReducer,
  ui: uiReducer,
  apiKeys: apiKeysReducer,
  network: networkReducer,
  // Add theme reducer for theme management
  themes: themeReducer,
  // Add user and project reducers
  user: userReducer,
  projects: projectReducer,
  // Add unified app builder reducer for component/layout integration
  appBuilder: appBuilderReducer
});

export default rootReducer;
