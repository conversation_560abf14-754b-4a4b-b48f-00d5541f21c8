/**
 * Component Workflow Demo
 * 
 * A simple demo component to test the integration between Component Builder and Layout Designer
 * This component allows manual testing of the workflow without complex test setup
 */

import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Button, Card, Space, Typography, Divider, Alert } from 'antd';
import { PlusOutlined, EyeOutlined } from '@ant-design/icons';

// Import the unified actions
import { addComponent } from '../../redux/actions/appBuilderActions';
import { getComponents } from '../../redux/selectors/appBuilderSelectors';

const { Title, Text } = Typography;

const ComponentWorkflowDemo = () => {
  const dispatch = useDispatch();
  const components = useSelector(getComponents);
  const [lastAddedComponent, setLastAddedComponent] = useState(null);

  // Test adding a component
  const handleAddTestComponent = () => {
    const testComponent = {
      id: `test-component-${Date.now()}`,
      type: 'button',
      name: `Test Button ${components.length + 1}`,
      props: {
        text: `Button ${components.length + 1}`,
        variant: 'primary'
      },
      createdAt: new Date().toISOString()
    };

    dispatch(addComponent(testComponent));
    setLastAddedComponent(testComponent);
  };

  const handleAddTextComponent = () => {
    const testComponent = {
      id: `test-text-${Date.now()}`,
      type: 'text',
      name: `Test Text ${components.length + 1}`,
      props: {
        content: `This is test text component ${components.length + 1}`,
        fontSize: '16px'
      },
      createdAt: new Date().toISOString()
    };

    dispatch(addComponent(testComponent));
    setLastAddedComponent(testComponent);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Component Workflow Integration Test</Title>
      
      <Alert
        message="Integration Test"
        description="This demo tests the integration between Component Builder and Layout Designer. Components added here should appear in the Layout Designer's component palette."
        type="info"
        showIcon
        style={{ marginBottom: '20px' }}
      />

      <Card title="Add Test Components" style={{ marginBottom: '20px' }}>
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleAddTestComponent}
          >
            Add Test Button Component
          </Button>
          <Button 
            type="default" 
            icon={<PlusOutlined />}
            onClick={handleAddTextComponent}
          >
            Add Test Text Component
          </Button>
        </Space>
        
        {lastAddedComponent && (
          <Alert
            message="Component Added!"
            description={`Added "${lastAddedComponent.name}" (${lastAddedComponent.type}) to the store.`}
            type="success"
            showIcon
            style={{ marginTop: '10px' }}
          />
        )}
      </Card>

      <Card title="Current Components in Store" style={{ marginBottom: '20px' }}>
        <Text strong>Total Components: {components.length}</Text>
        <Divider />
        
        {components.length === 0 ? (
          <Text type="secondary">No components in store yet. Add some components above!</Text>
        ) : (
          <div>
            {components.map((component, index) => (
              <div key={component.id} style={{ 
                padding: '8px', 
                border: '1px solid #d9d9d9', 
                borderRadius: '4px', 
                marginBottom: '8px',
                backgroundColor: '#fafafa'
              }}>
                <Space direction="vertical" size="small">
                  <Text strong>{component.name}</Text>
                  <Text type="secondary">Type: {component.type}</Text>
                  <Text type="secondary">ID: {component.id}</Text>
                  <Text type="secondary">Created: {new Date(component.createdAt).toLocaleTimeString()}</Text>
                </Space>
              </div>
            ))}
          </div>
        )}
      </Card>

      <Card title="Integration Status">
        <Space direction="vertical">
          <div>
            <EyeOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
            <Text>✅ Redux Store Integration: Working</Text>
          </div>
          <div>
            <EyeOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
            <Text>✅ Component Actions: Working</Text>
          </div>
          <div>
            <EyeOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
            <Text>✅ Component Selectors: Working</Text>
          </div>
          <Text type="secondary">
            Components added here should now be available in the Layout Designer's component palette.
            Navigate to the Layout Designer to verify the integration.
          </Text>
        </Space>
      </Card>
    </div>
  );
};

export default ComponentWorkflowDemo;
